// Hero Section (Porto Medical Style)
.hero {
  position: relative;
  height: 500px;
  overflow: hidden;

  &__slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    @include flex-center;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(45, 82, 159, 0.8); // Primary color overlay
      z-index: 1;
    }

    &--active {
      opacity: 1;
      z-index: 2;
    }

    &--1 {
      background-image: url("https://www.portotheme.com/wordpress/porto/medical/wp-content/uploads/sites/67/2024/02/medical-slide-1.jpg");
    }

    &--2 {
      background-image: url("https://www.portotheme.com/wordpress/porto/medical/wp-content/uploads/sites/67/2024/02/medical-slide-2.jpg");
    }
  }

  &__content {
    position: relative;
    z-index: 3;
    text-align: left;
    color: $light-color;
    max-width: 500px;
    padding: 0 20px;

    h1 {
      font-size: 3rem;
      font-weight: 300;
      margin-bottom: 15px;
      text-transform: capitalize;
      line-height: 1.2;

      @include mobile {
        font-size: 2rem;
      }
    }

    p {
      font-size: 18px;
      margin-bottom: 30px;
      line-height: 1.5;
      font-weight: 300;
    }
  }

  &__cta {
    @include flex-center;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: flex-start;
  }
}

// Medical Info Box
.medical-info {
  background-color: $light-color;
  padding: 40px 0;
  border-top: 3px solid $secondary-color;

  .container {
    @include flex-between;
    flex-wrap: wrap;
    gap: 30px;

    @include mobile {
      @include flex-column;
      text-align: center;
    }
  }

  &__item {
    @include flex-center;
    gap: 15px;

    &-icon {
      width: 60px;
      height: 60px;
      background-color: $secondary-color;
      border-radius: 50%;
      @include flex-center;
      color: $light-color;
      font-size: 1.5rem;
    }

    &-content {
      h3 {
        color: $dark-color;
        font-size: $small-header;
        margin-bottom: 5px;
        text-transform: uppercase;
      }

      p {
        color: $font-color;
        font-size: $small-font-size;
        margin: 0;
      }
    }
  }

  &__emergency {
    background-color: $primary-color;
    color: $light-color;
    padding: 20px;
    text-align: center;
    border-radius: 5px;

    h4 {
      margin-bottom: 10px;
      text-transform: uppercase;
    }

    a {
      color: $light-color;
      text-decoration: none;
      font-size: $base-font-size;
      font-weight: 700;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}
