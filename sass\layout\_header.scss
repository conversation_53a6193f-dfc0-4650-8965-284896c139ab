// Header Top Bar (Porto Medical Style)
.header-top {
  background-color: $header-bg;
  color: $light-color;
  padding: 8px 0;
  font-size: 13px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .container {
    @include flex-between;
  }

  &__contact {
    @include flex-center;
    gap: 25px;

    &-item {
      @include flex-center;
      gap: 8px;
      color: $light-color;
      text-decoration: none;
      font-size: 13px;

      i {
        font-size: 12px;
        color: $secondary-color;
      }

      &:hover {
        color: $secondary-color;
      }
    }
  }
}

// Main Header
.header {
  background-color: $light-color;
  padding: 15px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 0;
  z-index: 1000;

  .container {
    @include flex-between;
    align-items: center;
  }

  &__logo {
    img {
      height: 50px;
      width: auto;
    }
  }

  &__nav {
    @include flex-center;
    gap: 40px;

    &-link {
      color: $dark-color;
      text-decoration: none;
      font-weight: 500;
      font-size: 14px;
      text-transform: capitalize;
      position: relative;
      transition: color $transition-speed $transition-ease;
      padding: 10px 0;

      &:hover {
        color: $secondary-color;
      }

      &--active {
        color: $secondary-color;

        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: $secondary-color;
        }
      }
    }
  }

  &__mobile-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;

    @include mobile {
      display: block;
    }
  }

  @include mobile {
    &__nav {
      display: none;
    }
  }
}
