// Doctors Section
.doctors {
  padding: $section-padding 0;
  background-color: $gray-light;

  &__header {
    text-align: center;
    margin-bottom: 60px;

    h2 {
      color: $dark-color;
      font-size: $medium-header;
      margin-bottom: 15px;
      text-transform: capitalize;
      font-weight: 700;
    }

    p {
      color: $font-color;
      font-size: $base-font-size;
    }
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: $card-gap;
    margin-bottom: 50px;

    @include mobile {
      grid-template-columns: 1fr;
    }
  }

  &__cta {
    text-align: center;
  }
}
