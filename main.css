* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  font-family: "Arial", sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: #6c757d;
  background-color: #ffffff;
}

.container {
  max-width: 1200px;
  width: 90%;
  margin: 0 auto;
  padding: 0 20px;
}
@media (max-width: 767px) {
  .container {
    width: 95%;
    padding: 0 15px;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Arial", sans-serif;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 15px;
  color: #212529;
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 1.7rem;
}

h3 {
  font-size: 1.5rem;
}

p {
  margin-bottom: 15px;
  line-height: 1.6;
}

a {
  color: #008fe2;
  transition: color 0.3s ease-in-out;
}
a:hover {
  color: #2d529f;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: 10px;
}

.mb-2 {
  margin-bottom: 20px;
}

.mb-3 {
  margin-bottom: 30px;
}

.mb-4 {
  margin-bottom: 40px;
}

.mb-5 {
  margin-bottom: 50px;
}

.header-top {
  background-color: #2c3e50;
  color: #ffffff;
  padding: 8px 0;
  font-size: 13px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.header-top .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-top__contact {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 25px;
}
.header-top__contact-item {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  text-decoration: none;
  font-size: 13px;
}
.header-top__contact-item i {
  font-size: 12px;
  color: #008fe2;
}
.header-top__contact-item:hover {
  color: #008fe2;
}

.header {
  background-color: #ffffff;
  padding: 15px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 0;
  z-index: 1000;
}
.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-items: center;
}
.header__logo img {
  height: 50px;
  width: auto;
}
.header__nav {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
}
.header__nav-link {
  color: #212529;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  text-transform: capitalize;
  position: relative;
  transition: color 0.3s ease-in-out;
  padding: 10px 0;
}
.header__nav-link:hover {
  color: #008fe2;
}
.header__nav-link--active {
  color: #008fe2;
}
.header__nav-link--active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #008fe2;
}
.header__mobile-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}
@media (max-width: 767px) {
  .header__mobile-toggle {
    display: block;
  }
}
@media (max-width: 767px) {
  .header__nav {
    display: none;
  }
}

.hero {
  position: relative;
  height: 500px;
  overflow: hidden;
}
.hero__slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.hero__slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(45, 82, 159, 0.8);
  z-index: 1;
}
.hero__slide--active {
  opacity: 1;
  z-index: 2;
}
.hero__slide--1 {
  background-image: url("https://www.portotheme.com/wordpress/porto/medical/wp-content/uploads/sites/67/2024/02/medical-slide-1.jpg");
}
.hero__slide--2 {
  background-image: url("https://www.portotheme.com/wordpress/porto/medical/wp-content/uploads/sites/67/2024/02/medical-slide-2.jpg");
}
.hero__content {
  position: relative;
  z-index: 3;
  text-align: left;
  color: #ffffff;
  max-width: 500px;
  padding: 0 20px;
}
.hero__content h1 {
  font-size: 3rem;
  font-weight: 300;
  margin-bottom: 15px;
  text-transform: capitalize;
  line-height: 1.2;
}
@media (max-width: 767px) {
  .hero__content h1 {
    font-size: 2rem;
  }
}
.hero__content p {
  font-size: 18px;
  margin-bottom: 30px;
  line-height: 1.5;
  font-weight: 300;
}
.hero__cta {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.medical-info {
  background-color: #ffffff;
  padding: 40px 0;
  border-top: 3px solid #008fe2;
}
.medical-info .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 30px;
}
@media (max-width: 767px) {
  .medical-info .container {
    display: flex;
    flex-direction: column;
    text-align: center;
  }
}
.medical-info__item {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}
.medical-info__item-icon {
  width: 60px;
  height: 60px;
  background-color: #008fe2;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  font-size: 1.5rem;
}
.medical-info__item-content h3 {
  color: #212529;
  font-size: 1.5rem;
  margin-bottom: 5px;
  text-transform: uppercase;
}
.medical-info__item-content p {
  color: #6c757d;
  font-size: 0.875rem;
  margin: 0;
}
.medical-info__emergency {
  background-color: #2d529f;
  color: #ffffff;
  padding: 20px;
  text-align: center;
  border-radius: 5px;
}
.medical-info__emergency h4 {
  margin-bottom: 10px;
  text-transform: uppercase;
}
.medical-info__emergency a {
  color: #ffffff;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 700;
}
.medical-info__emergency a:hover {
  text-decoration: underline;
}

.about {
  padding: 80px 0;
  background-color: #ffffff;
}
.about .container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}
@media (max-width: 991px) {
  .about .container {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}
.about__content h2 {
  color: #212529;
  font-size: 1.7rem;
  margin-bottom: 20px;
  text-transform: capitalize;
  font-weight: 700;
}
.about__content p {
  color: #6c757d;
  line-height: 1.8;
  margin-bottom: 20px;
  font-size: 1rem;
}
.about__content .btn {
  margin-top: 20px;
}
.about__image {
  position: relative;
}
.about__image img {
  width: 100%;
  height: auto;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease-in-out;
}
.about__image img:hover {
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}
.about__image::after {
  content: "";
  position: absolute;
  top: 20px;
  left: 20px;
  width: 100%;
  height: 100%;
  border: 3px solid #008fe2;
  border-radius: 5px;
  z-index: -1;
}

.department {
  padding: 80px 0;
  background-color: #ffffff;
}
.department__header {
  text-align: center;
  margin-bottom: 60px;
}
.department__header h2 {
  color: #212529;
  font-size: 1.7rem;
  margin-bottom: 15px;
  text-transform: capitalize;
  font-weight: 700;
}
.department__header p {
  color: #6c757d;
  font-size: 1rem;
}
.department__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}
@media (max-width: 767px) {
  .department__grid {
    grid-template-columns: 1fr;
  }
}
.department__cta {
  text-align: center;
}

.doctors {
  padding: 80px 0;
  background-color: #f7f7f7;
}
.doctors__header {
  text-align: center;
  margin-bottom: 60px;
}
.doctors__header h2 {
  color: #212529;
  font-size: 1.7rem;
  margin-bottom: 15px;
  text-transform: capitalize;
  font-weight: 700;
}
.doctors__header p {
  color: #6c757d;
  font-size: 1rem;
}
.doctors__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}
@media (max-width: 767px) {
  .doctors__grid {
    grid-template-columns: 1fr;
  }
}
.doctors__cta {
  text-align: center;
}

.resources {
  padding: 80px 0;
  background-color: #ffffff;
}
.resources__header {
  text-align: center;
  margin-bottom: 60px;
}
.resources__header h2 {
  color: #212529;
  font-size: 1.7rem;
  margin-bottom: 15px;
  text-transform: capitalize;
  font-weight: 700;
}
.resources__header p {
  color: #6c757d;
  font-size: 1rem;
}
.resources__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}
@media (max-width: 767px) {
  .resources__grid {
    grid-template-columns: 1fr;
  }
}
.resources__cta {
  text-align: center;
}

.insurance {
  padding: 80px 0;
  background-color: #f7f7f7;
}
.insurance__header {
  text-align: center;
  margin-bottom: 60px;
}
.insurance__header h2 {
  color: #212529;
  font-size: 1.7rem;
  margin-bottom: 15px;
  text-transform: capitalize;
  font-weight: 700;
}
.insurance__header p {
  color: #6c757d;
  font-size: 1rem;
}
.insurance__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 30px;
  align-items: center;
}
@media (max-width: 767px) {
  .insurance__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
.insurance__logo {
  background-color: #ffffff;
  padding: 30px;
  border-radius: 5px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
}
.insurance__logo:hover {
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}
.insurance__logo:hover {
  transform: translateY(-3px);
}
.insurance__logo img {
  max-width: 100%;
  height: auto;
  max-height: 60px;
  filter: grayscale(100%);
  transition: filter 0.3s ease-in-out;
}
.insurance__logo:hover img {
  filter: grayscale(0%);
}

.testimonials {
  padding: 80px 0;
  background-color: #ffffff;
}
.testimonials__container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}
.testimonials__item {
  padding: 40px;
}
.testimonials__item-quote {
  font-size: 1rem;
  line-height: 1.8;
  color: #6c757d;
  margin-bottom: 30px;
  font-style: italic;
  position: relative;
}
.testimonials__item-quote::before {
  content: '"';
  font-size: 4rem;
  color: #008fe2;
  position: absolute;
  top: -20px;
  left: -20px;
  font-family: serif;
}
.testimonials__item-author {
  color: #212529;
  font-weight: 700;
  font-size: 1.5rem;
}
.testimonials__item-divider {
  width: 50px;
  height: 2px;
  background-color: #008fe2;
  margin: 20px auto;
}
.testimonials__nav {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 30px;
}
.testimonials__nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #dee2e6;
  cursor: pointer;
  transition: background-color 0.3s ease-in-out;
}
.testimonials__nav-dot--active {
  background-color: #008fe2;
}

.footer {
  background-color: #212529;
  color: #ffffff;
  padding: 60px 0 20px;
}
.footer__main {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .footer__main {
    grid-template-columns: 1fr;
    text-align: center;
  }
}
.footer__section h3 {
  color: #ffffff;
  font-size: 1.5rem;
  margin-bottom: 20px;
  text-transform: capitalize;
  font-weight: 700;
}
.footer__section p, .footer__section li {
  color: #6c757d;
  line-height: 1.8;
  margin-bottom: 10px;
}
.footer__section ul {
  list-style: none;
  padding: 0;
}
.footer__section a {
  color: #6c757d;
  text-decoration: none;
  transition: color 0.3s ease-in-out;
}
.footer__section a:hover {
  color: #008fe2;
}
.footer__contact-item {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  justify-content: flex-start;
}
@media (max-width: 767px) {
  .footer__contact-item {
    justify-content: center;
  }
}
.footer__contact-item-icon {
  color: #008fe2;
  font-size: 1.2rem;
}
.footer__social {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
}
@media (max-width: 767px) {
  .footer__social {
    justify-content: center;
  }
}
.footer__social-link {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background-color: #008fe2;
  color: #ffffff;
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease-in-out;
}
.footer__social-link:hover {
  background-color: #2d529f;
  transform: translateY(-2px);
}
.footer__bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 20px;
  text-align: center;
}
.footer__bottom p {
  color: #6c757d;
  font-size: 0.875rem;
  margin: 0;
}

.btn {
  display: inline-block;
  padding: 12px 30px;
  text-decoration: none;
  text-transform: uppercase;
  font-weight: 700;
  font-family: "Arial", sans-serif;
  font-size: 0.75rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  border-radius: 0;
}
.btn--primary {
  color: #008fe2;
  border: 2px solid #008fe2;
  background-color: transparent;
}
.btn--primary:hover {
  color: #ffffff;
  background-color: #008fe2;
  transition: all 0.3s ease-in-out;
}
.btn--secondary {
  color: #ffffff;
  background-color: #008fe2;
  border: 2px solid #008fe2;
}
.btn--secondary:hover {
  color: #ffffff;
  background-color: #2d529f;
  transition: all 0.3s ease-in-out;
}
.btn--dark {
  color: #212529;
  border: 2px solid #212529;
  background-color: transparent;
}
.btn--dark:hover {
  color: #ffffff;
  background-color: #212529;
  transition: all 0.3s ease-in-out;
}
.btn--light {
  color: #212529;
  background-color: #ffffff;
  border: 2px solid #ffffff;
}
.btn--light:hover {
  color: #212529;
  background-color: #ffffff;
  transition: all 0.3s ease-in-out;
}
.btn--large {
  padding: 15px 40px;
  font-size: 0.875rem;
}
.btn--small {
  padding: 8px 20px;
  font-size: 0.75rem;
}

.sec-title {
  text-align: center;
  margin-bottom: 50px;
}
.sec-title__header {
  color: #212529;
  font-family: "Arial", sans-serif;
  font-size: 1.7rem;
  margin-bottom: 15px;
  font-weight: 700;
  text-transform: capitalize;
  line-height: 1.2;
}
.sec-title__subtitle {
  color: #008fe2;
  font-size: 0.875rem;
  text-transform: uppercase;
  font-weight: 600;
  margin-bottom: 10px;
  letter-spacing: 1px;
}
.sec-title p {
  color: #6c757d;
  font-size: 1rem;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}
.sec-title--left {
  text-align: left;
  margin-bottom: 40px;
}
.sec-title--left p {
  margin: 0;
}
.sec-title--large .sec-title__header {
  font-size: 2.5rem;
}

.card-icon {
  display: flex;
  gap: 20px;
  padding: 0;
  background-color: transparent;
  transition: transform 0.3s ease-in-out;
}
.card-icon:hover {
  transform: translateY(-3px);
}
.card-icon__img {
  flex-shrink: 0;
}
.card-icon__img img {
  width: 50px;
  height: 50px;
  object-fit: contain;
}
.card-icon__text {
  flex: 1;
}
.card-icon__text h3 {
  color: #008fe2;
  text-transform: capitalize;
  font-size: 1.5rem;
  margin-bottom: 15px;
  font-weight: 700;
}
.card-icon__text h3 a {
  color: inherit;
  text-decoration: none;
}
.card-icon__text h3 a:hover {
  color: #2d529f;
}
.card-icon__text p {
  color: #6c757d;
  line-height: 1.6;
  font-size: 0.875rem;
  margin: 0;
}
.card-icon--vertical {
  flex-direction: column;
  text-align: center;
}
.card-icon--vertical .card-icon__img {
  align-self: center;
  margin-bottom: 10px;
}

.doctor-card {
  background-color: #ffffff;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
}
.doctor-card:hover {
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}
.doctor-card:hover {
  transform: translateY(-5px);
}
.doctor-card__image {
  position: relative;
  overflow: hidden;
}
.doctor-card__image img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease-in-out;
}
.doctor-card__image:hover img {
  transform: scale(1.05);
}
.doctor-card__image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
}
.doctor-card__image-overlay .btn {
  background-color: #008fe2;
  color: #ffffff;
  padding: 8px 16px;
  font-size: 0.75rem;
  border-radius: 20px;
}
.doctor-card__image-overlay .btn:hover {
  background-color: #2d529f;
}
.doctor-card__image:hover .doctor-card__image-overlay {
  transform: translateY(0);
}
.doctor-card__content {
  padding: 20px;
  text-align: center;
}
.doctor-card__content-specialty {
  color: #008fe2;
  font-size: 0.875rem;
  text-transform: uppercase;
  font-weight: 600;
  margin-bottom: 8px;
}
.doctor-card__content-name {
  color: #212529;
  font-size: 1.5rem;
  margin-bottom: 15px;
  font-weight: 700;
}
.doctor-card__content-name a {
  color: inherit;
  text-decoration: none;
}
.doctor-card__content-name a:hover {
  color: #008fe2;
}
.doctor-card__content-link {
  color: #008fe2;
  font-size: 1.2rem;
  text-decoration: none;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border: 2px solid #008fe2;
  border-radius: 50%;
  margin: 0 auto;
  transition: all 0.3s ease-in-out;
}
.doctor-card__content-link:hover {
  background-color: #008fe2;
  color: #ffffff;
}

.resource-card {
  background-color: #ffffff;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
}
.resource-card:hover {
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}
.resource-card:hover {
  transform: translateY(-3px);
}
.resource-card__image {
  position: relative;
  overflow: hidden;
  height: 200px;
}
.resource-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease-in-out;
}
.resource-card__image:hover img {
  transform: scale(1.05);
}
.resource-card__content {
  padding: 25px;
}
.resource-card__content-title {
  color: #212529;
  font-size: 1.5rem;
  margin-bottom: 15px;
  font-weight: 700;
}
.resource-card__content-title a {
  color: inherit;
  text-decoration: none;
}
.resource-card__content-title a:hover {
  color: #008fe2;
}
.resource-card__content-excerpt {
  color: #6c757d;
  line-height: 1.6;
  font-size: 0.875rem;
  margin-bottom: 20px;
}
.resource-card__content-link {
  color: #008fe2;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
}
.resource-card__content-link:hover {
  color: #2d529f;
  text-decoration: underline;
}

/*# sourceMappingURL=main.css.map */
