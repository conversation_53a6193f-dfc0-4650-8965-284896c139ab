// Insurance Section
.insurance {
  padding: $section-padding 0;
  background-color: $gray-light;

  &__header {
    text-align: center;
    margin-bottom: 60px;

    h2 {
      color: $dark-color;
      font-size: $medium-header;
      margin-bottom: 15px;
      text-transform: capitalize;
      font-weight: 700;
    }

    p {
      color: $font-color;
      font-size: $base-font-size;
    }
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 30px;
    align-items: center;

    @include mobile {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &__logo {
    background-color: $light-color;
    padding: 30px;
    border-radius: 5px;
    text-align: center;
    @include card-shadow;
    transition: transform $transition-speed $transition-ease;

    &:hover {
      transform: translateY(-3px);
    }

    img {
      max-width: 100%;
      height: auto;
      max-height: 60px;
      filter: grayscale(100%);
      transition: filter $transition-speed $transition-ease;
    }

    &:hover img {
      filter: grayscale(0%);
    }
  }
}
